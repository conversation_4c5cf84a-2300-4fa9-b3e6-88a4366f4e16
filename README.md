## paper-scraper

Fetch arXiv metadata for a date, cache first-page text, and extract author affiliations with an LLM (OpenAI or Ollama). Outputs are saved under the data directory.

### Requirements
- Python 3.9+
- Dependencies: openai, requests, bs4, PyPDF2
- For provider=openai: environment variable OPENAI_API_KEY
- For provider=ollama: a running Ollama server at http://localhost:11434 and a pulled model (e.g., qwen3:4b-instruct)

### Quick start

1) Install (editable):
```
pip3 install -e .
```

2) Copy env example and edit:
```
cp .env.example .env
# then edit .env (OPENAI_API_KEY, optional base URLs, etc.)
```

3) Data layout (standardized):
- data/metadata/YY/MM/DD/metadata.json
- data/pages/YY/MM/DD/<arxiv_id>.txt
- data/affiliations/YY/MM/DD/affiliations.json

### CLI (package entrypoint)

```
# Fetch metadata
paperscraper metadata YYYY-MM-DD [--num 5] [--force] [-v]

# Cache first-page text for the same day
paperscraper pages YYYY-MM-DD [--num 5] [--force] [-v]

# Extract affiliations (OpenAI)
export OPENAI_API_KEY=...  # required for provider=openai
paperscraper affiliations --date YYYY-MM-DD --provider openai --model gpt-5-nano -v

# Extract affiliations (Ollama)
ollama pull qwen3:4b-instruct
paperscraper affiliations --date YYYY-MM-DD --provider ollama --model qwen3:4b-instruct -v

# Run affiliations from a custom metadata file
paperscraper affiliations --input-file path/to/metadata.json --provider ollama --model qwen3:4b-instruct
```

### Providers
- openai: uses base https://api.openai.com/v1 (override with --base-url if needed)
- ollama: uses base http://localhost:11434/v1 and works with the OpenAI Python client via chat.completions

### Stats and costs
The affiliations command prints aggregate statistics (token usage, processing times per source, and estimated cost using the notebook formula). Results are also saved as JSON with per-paper details.

### Notes
- Category is cs.AI and date range is UTC day. API calls are paginated with a delay per arXiv guidance.
- First page text is parsed from arXiv HTML when available; otherwise PDF is downloaded and parsed (first page only).
- Organizations list for notable affiliations is read from orgs.txt (customizable with --orgs).
