{"count": 5, "items": [{"id": "2408.00203v1", "abs_url": "https://arxiv.org/abs/2408.00203", "pdf_url": "https://arxiv.org/pdf/2408.00203v1", "title": "OmniParser for Pure Vision Based GUI Agent", "summary": "The recent success of large vision language models shows great potential in\ndriving the agent system operating on user interfaces. However, we argue that\nthe power multimodal models like GPT-4V as a general agent on multiple\noperating systems across different applications is largely underestimated due\nto the lack of a robust screen parsing technique capable of: 1) reliably\nidentifying interactable icons within the user interface, and 2) understanding\nthe semantics of various elements in a screenshot and accurately associate the\nintended action with the corresponding region on the screen. To fill these\ngaps, we introduce \\textsc{OmniParser}, a comprehensive method for parsing user\ninterface screenshots into structured elements, which significantly enhances\nthe ability of GPT-4V to generate actions that can be accurately grounded in\nthe corresponding regions of the interface. We first curated an interactable\nicon detection dataset using popular webpages and an icon description dataset.\nThese datasets were utilized to fine-tune specialized models: a detection model\nto parse interactable regions on the screen and a caption model to extract the\nfunctional semantics of the detected elements. \\textsc{OmniParser}\nsignificantly improves GPT-4V's performance on ScreenSpot benchmark. And on\nMind2Web and AITW benchmark, \\textsc{OmniParser} with screenshot only input\noutperforms the GPT-4V baselines requiring additional information outside of\nscreenshot.", "published": "2024-08-01T00:00:43Z", "updated": "2024-08-01T00:00:43Z", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "primary_category": "cs.CV", "categories": ["cs.CV", "cs.AI", "cs.CL", "cs.LG"], "affiliations": {"paper_title": "OmniParser for Pure Vision Based GUI Agent", "all_affiliations": [{"author_name": "<PERSON><PERSON>", "organization": "Microsoft Research", "is_notable": true}, {"author_name": "<PERSON><PERSON><PERSON>", "organization": "Microsoft Research", "is_notable": true}, {"author_name": "<PERSON><PERSON>", "organization": "Microsoft Gen AI", "is_notable": true}, {"author_name": "<PERSON>", "organization": "Microsoft Research", "is_notable": true}], "notable_affiliations": [{"author_name": "<PERSON><PERSON>", "organization": "Microsoft Research"}, {"author_name": "<PERSON><PERSON><PERSON>", "organization": "Microsoft Research"}, {"author_name": "<PERSON><PERSON>", "organization": "Microsoft Gen AI"}, {"author_name": "<PERSON>", "organization": "Microsoft Research"}], "has_notable_affiliations": true}, "input_tokens": 935, "output_tokens": 206, "paper_source": "cache", "first_page_processing_time": 0.0, "affiliation_processing_time": 11.34, "total_processing_time": 11.34}, {"id": "2408.00210v1", "abs_url": "https://arxiv.org/abs/2408.00210", "pdf_url": "https://arxiv.org/pdf/2408.00210v1", "title": "A Prior Embedding-Driven Architecture for Long Distance Blind Iris\n  Recognition", "summary": "Blind iris images, which result from unknown degradation during the process\nof iris recognition at long distances, often lead to decreased iris recognition\nrates. Currently, little existing literature offers a solution to this problem.\nIn response, we propose a prior embedding-driven architecture for long distance\nblind iris recognition. We first proposed a blind iris image restoration\nnetwork called Iris-PPRGAN. To effectively restore the texture of the blind\niris, Iris-PPRGAN includes a Generative Adversarial Network (GAN) used as a\nPrior Decoder, and a DNN used as the encoder. To extract iris features more\nefficiently, we then proposed a robust iris classifier by modifying the\nbottleneck module of InsightFace, which called Insight-Iris. A low-quality\nblind iris image is first restored by Iris-PPRGAN, then the restored iris image\nundergoes recognition via Insight-Iris. Experimental results on the public\nCASIA-Iris-distance dataset demonstrate that our proposed method significantly\nsuperior results to state-of-the-art blind iris restoration methods both\nquantitatively and qualitatively, Specifically, the recognition rate for\nlong-distance blind iris images reaches 90% after processing with our methods,\nrepresenting an improvement of approximately ten percentage points compared to\nimages without restoration.", "published": "2024-08-01T00:40:17Z", "updated": "2024-08-01T00:40:17Z", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "primary_category": "cs.CV", "categories": ["cs.CV", "cs.AI"], "affiliations": {"paper_title": "A Prior Embedding-Driven Architecture for Long Distance Blind Iris Recognition", "all_affiliations": [{"author_name": "<PERSON>", "organization": "International College, Hunan University of Arts and Sciences, Changde 415 000, China", "is_notable": false}, {"author_name": "<PERSON><PERSON><PERSON>", "organization": "School of Automation Science and Engineering, Faculty of Electronic and Information Engineering; MOE Key Lab for Intelligent Networks and Network Security, Xi’an Jiaotong University, Xi’an 710049, Shaanxi, China", "is_notable": false}, {"author_name": "<PERSON>", "organization": "School of Computing and Information Technology, University of Wollongong, Wollongong, NSW 2522, Australia", "is_notable": false}], "notable_affiliations": [], "has_notable_affiliations": false}, "input_tokens": 928, "output_tokens": 206, "paper_source": "cache", "first_page_processing_time": 0.0, "affiliation_processing_time": 10.47, "total_processing_time": 10.47}, {"id": "2408.00230v2", "abs_url": "https://arxiv.org/abs/2408.00230", "pdf_url": "https://arxiv.org/pdf/2408.00230v2", "title": "Lost in Translation: Latent Concept Misalignment in Text-to-Image\n  Diffusion Models", "summary": "Advancements in text-to-image diffusion models have broadened extensive\ndownstream practical applications, but such models often encounter misalignment\nissues between text and image. Taking the generation of a combination of two\ndisentangled concepts as an example, say given the prompt \"a tea cup of iced\ncoke\", existing models usually generate a glass cup of iced coke because the\niced coke usually co-occurs with the glass cup instead of the tea one during\nmodel training. The root of such misalignment is attributed to the confusion in\nthe latent semantic space of text-to-image diffusion models, and hence we refer\nto the \"a tea cup of iced coke\" phenomenon as Latent Concept Misalignment\n(LC-Mis). We leverage large language models (LLMs) to thoroughly investigate\nthe scope of LC-Mis, and develop an automated pipeline for aligning the latent\nsemantics of diffusion models to text prompts. Empirical assessments confirm\nthe effectiveness of our approach, substantially reducing LC-Mis errors and\nenhancing the robustness and versatility of text-to-image diffusion models. The\ncode and dataset are here: https://github.com/RossoneriZhao/iced_coke.", "published": "2024-08-01T01:54:17Z", "updated": "2024-08-05T08:36:20Z", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chong<PERSON>uan Li", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "primary_category": "cs.AI", "categories": ["cs.AI", "cs.CL"], "affiliations": {"paper_title": "Lost in Translation: Latent Concept Misalignment in Text-to-Image Diffusion Models", "all_affiliations": [{"author_name": "<PERSON><PERSON>", "organization": "Shanghai Jiao Tong University", "is_notable": false}, {"author_name": "<PERSON><PERSON>", "organization": "Shanghai Artificial Intelligence Laboratory", "is_notable": false}, {"author_name": "<PERSON><PERSON>", "organization": "Fudan University", "is_notable": false}, {"author_name": "Chong<PERSON>uan Li", "organization": "Renmin University of China", "is_notable": false}, {"author_name": "<PERSON><PERSON><PERSON><PERSON>", "organization": "Shanghai Jiao Tong University", "is_notable": false}, {"author_name": "<PERSON><PERSON><PERSON>", "organization": "Shanghai Jiao Tong University", "is_notable": false}], "notable_affiliations": [], "has_notable_affiliations": false}, "input_tokens": 1121, "output_tokens": 209, "paper_source": "cache", "first_page_processing_time": 0.0, "affiliation_processing_time": 11.75, "total_processing_time": 11.76}, {"id": "2408.00241v3", "abs_url": "https://arxiv.org/abs/2408.00241", "pdf_url": "https://arxiv.org/pdf/2408.00241v3", "title": "Multiple Greedy Quasi-Newton Methods for Saddle Point Problems", "summary": "This paper introduces the Multiple Greedy Quasi-Newton (MGSR1-SP) method, a\nnovel approach to solving strongly-convex-strongly-concave (SCSC) saddle point\nproblems. Our method enhances the approximation of the squared indefinite\nHessian matrix inherent in these problems, significantly improving both\nstability and efficiency through iterative greedy updates. We provide a\nthorough theoretical analysis of MGSR1-SP, demonstrating its linear-quadratic\nconvergence rate. Numerical experiments conducted on AUC maximization and\nadversarial debiasing problems, compared with state-of-the-art algorithms,\nunderscore our method's enhanced convergence rate. These results affirm the\npotential of MGSR1-SP to improve performance across a broad spectrum of machine\nlearning applications where efficient and accurate Hessian approximations are\ncrucial.", "published": "2024-08-01T02:40:37Z", "updated": "2025-06-10T20:52:51Z", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "primary_category": "cs.AI", "categories": ["cs.AI"], "affiliations": {"paper_title": "Multiple Greedy Quasi-Newton Methods for Saddle Point Problems", "all_affiliations": [{"author_name": "<PERSON><PERSON>", "organization": "dept. Integrated System Engineering, Ohio State University", "is_notable": false}, {"author_name": "<PERSON><PERSON><PERSON><PERSON>", "organization": "dept. Engineering, UC Berkeley", "is_notable": true}], "notable_affiliations": [{"author_name": "<PERSON><PERSON><PERSON><PERSON>", "organization": "dept. Engineering, UC Berkeley"}], "has_notable_affiliations": true}, "input_tokens": 923, "output_tokens": 126, "paper_source": "cache", "first_page_processing_time": 0.0, "affiliation_processing_time": 7.82, "total_processing_time": 7.82}, {"id": "2408.00257v1", "abs_url": "https://arxiv.org/abs/2408.00257", "pdf_url": "https://arxiv.org/pdf/2408.00257v1", "title": "RoCo:Robust Collaborative Perception By Iterative Object Matching and\n  Pose Adjustment", "summary": "Collaborative autonomous driving with multiple vehicles usually requires the\ndata fusion from multiple modalities. To ensure effective fusion, the data from\neach individual modality shall maintain a reasonably high quality. However, in\ncollaborative perception, the quality of object detection based on a modality\nis highly sensitive to the relative pose errors among the agents. It leads to\nfeature misalignment and significantly reduces collaborative performance. To\naddress this issue, we propose RoCo, a novel unsupervised framework to conduct\niterative object matching and agent pose adjustment. To the best of our\nknowledge, our work is the first to model the pose correction problem in\ncollaborative perception as an object matching task, which reliably associates\ncommon objects detected by different agents. On top of this, we propose a graph\noptimization process to adjust the agent poses by minimizing the alignment\nerrors of the associated objects, and the object matching is re-done based on\nthe adjusted agent poses. This process is carried out iteratively until\nconvergence. Experimental study on both simulated and real-world datasets\ndemonstrates that the proposed framework RoCo consistently outperforms existing\nrelevant methods in terms of the collaborative object detection performance,\nand exhibits highly desired robustness when the pose information of agents is\nwith high-level noise. Ablation studies are also provided to show the impact of\nits key parameters and components. The code is released at\nhttps://github.com/HuangZhe885/RoCo.", "published": "2024-08-01T03:29:33Z", "updated": "2024-08-01T03:29:33Z", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wanting Li", "<PERSON><PERSON>", "<PERSON><PERSON>"], "primary_category": "cs.AI", "categories": ["cs.AI"], "affiliations": {"paper_title": "RoCo:Robust Collaborative Perception By Iterative Object Matching and Pose Adjustment", "all_affiliations": [{"author_name": "<PERSON><PERSON>", "organization": "School of Information, Renmin University of ChinaBeijingChina", "is_notable": false}, {"author_name": "<PERSON><PERSON>", "organization": "School of Information, Renmin University of ChinaBeijingChina", "is_notable": false}, {"author_name": "<PERSON><PERSON><PERSON>", "organization": "School of Information, Renmin University of ChinaBeijingChina", "is_notable": false}, {"author_name": "Wanting Li", "organization": "School of Information, Renmin University of ChinaBeijingChina", "is_notable": false}, {"author_name": "<PERSON><PERSON>", "organization": "School of Information, Renmin University of ChinaBeijingChina", "is_notable": false}, {"author_name": "<PERSON><PERSON>", "organization": "University of WollongongWollongongNSWAustralia", "is_notable": false}], "notable_affiliations": [], "has_notable_affiliations": false}, "input_tokens": 1083, "output_tokens": 248, "paper_source": "cache", "first_page_processing_time": 0.0, "affiliation_processing_time": 13.19, "total_processing_time": 13.19}]}