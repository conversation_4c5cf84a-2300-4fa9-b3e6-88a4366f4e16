
RoCo: Robust Collaborative Perception By Iterative Object Matching and Pose Adjustment
1 Introduction
2 Related work
2.1 Collaborative Perception
2.2 Noisy Pose Issue
2.3 Object Matching 
3 Collaborative Perception and the Issue of Pose Error
4 Our Proposed Method
4.1 Object Detection
4.2 Graph-guided Object Matching
4.2.1 Graph construction and Initial Matching.
4.2.2 Graph Structure Similarity.
4.3 Robust Pose Graph Optimization
4.4 Aggregation and Detection
5 Experimental Result
5.1 Dataset
5.2 Implementation Details
5.3 Quantitative evaluation
5.4 Qualitative evaluation
5.5 Ablation Studies
6 Conclusion
RoCo: Robust Collaborative Perception By Iterative Object Matching and Pose Adjustment
Zhe Huang
0009-0005-7656-1298
School of Information, Renmin University of ChinaBeijingChina
<EMAIL>
, 
Shu<PERSON>
0000-0002-6720-1646
School of Information, Renmin University of ChinaBeijingChina
<EMAIL>
, 
Yong<PERSON>i Wang
0000-0002-4197-2258
School of Information, Renmin University of ChinaBeijingChina
<EMAIL>
, 
Wanting Li
0000-0002-0092-7020
School of Information, Renmin University of ChinaBeijingChina
<EMAIL>
, 
Deying Li
0000-0002-7748-5427
School of Information, Renmin University of ChinaBeijingChina
<EMAIL>
 and 
Lei Wang
0000-0002-0961-0441
University of WollongongWollongongNSWAustralia
lei˙<EMAIL>
(2018; 2024)
Abstract.
Collaborative autonomous driving with multiple vehicles
usually requires the data fusion from mul