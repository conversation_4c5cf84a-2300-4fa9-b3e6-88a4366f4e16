
Lost in Translation: Latent Concept Misalignment in Text-to-Image Diffusion Models
1 Introduction
2 Related Work
3 Benchmark: Collecting Data on Latent Concept Misalignment (LC-Mis)
3.1 Phase 1 - Identifying Initial Concept Pairs as Seeds
3.2 Phase 2 - Generating and Verifying Additional Concept Pairs with GPT-3.5
3.3 Phase 3 - Discovering New Patterns for Concept Pair Generation
3.4 Phase 4 - Creating Novel Concept Pairs by Merging Patterns
4 Method: Mixture of Concept Experts (MoCE)
5 Experiments
5.1 Setup
5.2 Result
5.3 Analysis
6 Conclusions
6.1 Summary
6.2 Recent Advances in the Field
6.3 Future Work
0.A Interaction Details in Interactive LLMs Guidance System
0.B Interaction in Sequential Concept Introduction
0.C Comprehensive Explanation of MoCE
0.D Score Evaluation
0.E Ablation Demo
0.F Restoration Visualizations of Level 5
0.G Restoration Visualizations of Level 1 - 4
11institutetext: 
Shanghai Jiao Tong University 22institutetext: Shanghai Artificial Intelligence Laboratory 33institutetext: Fudan University 44institutetext: Renmin University of China
Lost in Translation: Latent Concept Misalignment in Text-to-Image Diffusion Models
Juntu Zhao\orcidlink0009-0003-6638-2283
11
  
Junyu Deng*\orcidlink0009-0004-6313-7378
33
  
Yi<PERSON> Ye*\orcidlink0009-0006-1882-2960
11
  
Chongxuan Li\orcidlink0000-0002-0912-9076
44
  
Zhijie Deng†\orcidlink0000-0002-0932-1631
11
  
Dequan Wang†\orcidlink0000-0001-**************
Abstract
Advancements in text-to-image diffusion models hav