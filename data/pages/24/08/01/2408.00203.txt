
OmniParser for Pure Vision Based GUI Agent
1 Introduction
2 Related Works
2.1 UI Screen Understanding
2.2 Autonomous GUI Agent
3 Methods
3.1 Interactable Region Detection
3.2 Incorporating Local Semantics of Functionality
4 Experiments and Results
4.1 Evaluation on SeeAssign Task
4.2 Evaluation on ScreenSpot
4.3 Evaluation on Mind2Web
4.4 Evaluation on AITW
5 Discussions
6 Conclusion
7 Appendix
7.1 Details of Icon-Description Dataset
7.2 Training details of Interactable Icon Region Detection Model
7.3 Details of SeeAssign Evaluation
7.3.1 Prompt Used for GPT-4V
7.4 Details of Mind2Web Evaluation
7.4.1 Qualitative Examples
OmniParser for Pure Vision Based GUI Agent
Ya<PERSON>1, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>
1Microsoft Research  2 Microsoft Gen AI 
{yadonglu, jianwei.yang, yeshe, ahmed.awadallah}@microsoft.com
Abstract
The recent success of large vision language models shows great potential in driving the agent system operating on user interfaces. However, we argue that the power multimodal models like GPT-4V as a general agent on multiple operating systems across different applications is largely underestimated due to the lack of a robust screen parsing technique capable of: 1) reliably identifying interactable icons within the user interface, and 2) understanding the semantics of various elements in a screenshot and accurately associate the intended action with the corresponding region on the screen. To fill these gaps, we introduce OmniParser, a comprehensive