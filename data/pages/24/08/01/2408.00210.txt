 
 
A Prior Embedding -Driven Architecture  for Long Distance Blind Iris Recognition    
Qi Xiong1,2 
1, International Colle ge, Hunan University of Arts and Sciences, Changde 415000, China  
Xinman Zhang2,* 
2, School of Automation Science and Engineering, Faculty of Electronic and Information 
Engineering; MOE Key Lab for Intelligent Networks and Network Security, Xi ’an  
Jiaotong University, Xi ’an 710049, S<PERSON><PERSON>xi, China  
Jun Shen3 
3, School of Comp uting and Information Technology, University of Wollongong,  
Wollongong, NSW 2522, Australia  
_______________________________________________________________________________  
Abstract: Blind iris images, which result from unknown degradation during the process 
of iris recognition at long distances, often lead to decreased iris recognition rates.  
Currently, little existing literature offers a solution to this prob lem. In response, we 
propose a  prior embedding -driven architecture  for long distance blind ir is recognition . 
We first proposed a blind iris image restoration network called Iris -PPRGAN. To 
effectively restore the texture of the blind iris, Iris-PPRGAN includes a Generative 
Adversarial Network (GAN) used as a Prior Decoder, and a DNN used as the encoder . 
To extract iris features more efficiently , we then proposed  a robust iris classifier  by 
modifying the bottleneck module of InsightFac e, which called Insight -Iris. A low-
quality blind iris image is first restore d by Iris -PPRGAN, then the res