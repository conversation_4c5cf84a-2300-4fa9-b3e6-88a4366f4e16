
Multiple Greedy Quasi-Newton Methods for Saddle Point Problems
I Introduction
II Notation and Preliminaries
III Methodology and Theoretical Analysis
III-A A Quasi-Newton Framework for Saddle Point Problems
III-B Greedy Quasi-Newton Methods
III-C MGSR1-SP Algorithm and Convergence Analysis
IV Numerical Experiments
IV-A AUC Maximization
IV-B Adversarial Debiasing
IV-C Analysis
V Conclusion
VI Acknowledgment
Multiple Greedy Quasi-Newton Methods for Saddle Point Problems
1st Minheng Xiao
dept. Integrated System Engineering
Ohio State University
Columbus, USA 
<EMAIL>
  
2nd Zhizhong Wu
dept. Engineering
UC Berkeley
Berkeley, USA 
<EMAIL>
Abstract
This paper introduces the Multiple Greedy Quasi-Newton (MGSR1-SP) method, a novel approach designed to solve strongly-convex-strongly-concave (SCSC) saddle point problems. Our method enhances the approximation of the square of the indefinite Hessian matrix inherent in these problems, significantly improving both the accuracy and efficiency through iterative greedy updates. We provide a thorough theoretical analysis of MGSR1-SP, demonstrating its linear-quadratic convergence properties. Numerical experiments conducted on AUC maximization and adversarial debiasing problems, compared with state-of-the-art algorithms, underscore our method’s enhanced convergence rates and superior quality in inverse Hessian estimation. These results affirm the potential of MGSR1-SP to improve performance across a broad spectrum o