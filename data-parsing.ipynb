{"cells": [{"cell_type": "markdown", "id": "5b79285a", "metadata": {}, "source": ["# Research Author Affiliation Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "77397064", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Date: 2024-08-01\n", "Category: cs.AI\n", "Paper count: 98\n", "---\n", "Title: OmniParser for Pure Vision Based GUI Agent\n", "Authors: <AUTHORS>\n", "Abstract URL: https://arxiv.org/abs/2408.00203\n", "---\n", "98 more papers...\n"]}], "source": ["import json\n", "import requests\n", "from pypdf import PdfReader\n", "from io import BytesIO\n", "\n", "\n", "# Open and load the metadata.json file\n", "with open('data/2024-08-01/metadata.json', 'r', encoding='utf-8') as f:\n", "    metadata = json.load(f)\n", "\n", "# Access the data\n", "print(f\"Date: {metadata['date']}\")\n", "print(f\"Category: {metadata['category']}\")\n", "print(f\"Paper count: {metadata['count']}\\n---\")\n", "\n", "# Example: iterate through papers\n", "for paper in metadata['papers']:\n", "    print(f\"Title: {paper['title']}\")\n", "    print(f\"Authors: <AUTHORS>\n", "    print(f\"Abstract URL: {paper['abs_url']}\")\n", "    print(\"---\")\n", "    break\n", "\n", "print(f\"{len(metadata['papers'])} more papers...\")"]}, {"cell_type": "code", "execution_count": null, "id": "cfff850e", "metadata": {}, "outputs": [], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "import re\n", "from io import BytesIO\n", "from PyPDF2 import PdfReader\n", "\n", "def getHTMLFirstPageText(paper, char_limit=1500):\n", "    arxiv_id = paper[\"abs_url\"].split(\"/\")[-1]\n", "    html_url = f\"https://arxiv.org/html/{arxiv_id}\"\n", "    \n", "    response = requests.get(html_url, timeout=30)\n", "    response.raise_for_status()  # Raises HTTPError for 4xx/5xx\n", "\n", "    soup = BeautifulSoup(response.content, 'html.parser')\n", "    text = soup.get_text()\n", "\n", "    text = re.sub(r'[ \\t]+', ' ', text)\n", "    text = re.sub(r'\\n+', '\\n', text)\n", "\n", "    return text[:char_limit]\n", "\n", "def getPDFFirstPageText(paper, char_limit=1500):\n", "    pdf_url = paper[\"pdf_url\"]\n", "    response = requests.get(pdf_url, timeout=30)\n", "    response.raise_for_status()\n", "\n", "    pdf = PdfReader(BytesIO(response.content))\n", "    text = pdf.pages[0].extract_text()\n", "    return text[:char_limit]\n", "\n", "def getFirstPageText(paper, char_limit=1500):\n", "    arxiv_id = paper[\"abs_url\"].split(\"/\")[-1]\n", "    \n", "    try:\n", "        # Try HTML first\n", "        return {\n", "            \"source\": \"html\",\n", "            \"text\": getHTMLFirstPageText(paper, char_limit)\n", "        }\n", "    except requests.exceptions.HTTPError as e:\n", "        if e.response.status_code == 404:\n", "            # print(f\"HTML page not found for {arxiv_id} (404), falling back to PDF\")\n", "            return {\n", "                \"source\": \"pdf\",\n", "                \"text\": getPDFFirstPageText(paper, char_limit),\n", "                \"error\": e\n", "            }\n", "        else:\n", "            # print(f\"HTTP error for {arxiv_id}: {e}\")\n", "            return {\n", "                \"source\": \"none\",\n", "                \"text\": \"\",\n", "                \"error\": e\n", "            }\n", "    except requests.exceptions.RequestException as e:\n", "        # print(f\"Request error for {arxiv_id}: {e}\")\n", "        return {\n", "                \"source\": \"none\",\n", "                \"text\": \"\",\n", "                \"error\": e\n", "            }\n"]}, {"cell_type": "code", "execution_count": null, "id": "0423be82", "metadata": {}, "outputs": [{"data": {"text/plain": ["PaperAffilationAnalysis(paper_title='A Prior Embedding-Driven Architecture for Long Distance Blind Iris Recognition', all_affiliations=[AllAuthorAffiliation(author_name='<PERSON>', organization='1, International Colle ge, Hunan University of Arts and Sciences, Changde 415000, China', is_notable=False), AllAuthorAffiliation(author_name='<PERSON><PERSON><PERSON>', organization='2, School of Automation Science and Engineering, Faculty of Electronic and Information Engineering; MOE Key Lab for Intelligent Networks and Network Security, Xi ’an Jiaotong University, Xi ’an 710049, Shaanxi, China', is_notable=False), AllAuthorAffiliation(author_name='Jiaotong University', organization='', is_notable=False), AllAuthorAffiliation(author_name='<PERSON>', organization='3, School of Computing and Information Technology, University of Wollongong, Wollongong, NSW 2522, Australia', is_notable=False)], notable_affiliations=[], has_notable_affiliations=False)"]}, "execution_count": 127, "metadata": {}, "output_type": "execute_result"}], "source": ["from pydantic import BaseModel\n", "from typing import List, Optional\n", "from openai import OpenAI\n", "import os\n", "\n", "\n", "import dotenv\n", "dotenv.load_dotenv()\n", "\n", "\n", "class AuthorAffiliation(BaseModel):\n", "    author_name: str\n", "    organization: str\n", "\n", "class AllAuthorAffiliation(BaseModel):\n", "    author_name: str\n", "    organization: str\n", "    is_notable: bool\n", "\n", "class PaperAffilationAnalysis(BaseModel):\n", "    paper_title: str\n", "    all_affiliations: List[AllAuthorAffiliation]\n", "    notable_affiliations: List[AuthorAffiliation]\n", "    has_notable_affiliations: bool\n", "\n", "ollama_api_base = \"http://localhost:11434/v1\"\n", "openai_api_base = \"https://api.openai.com/v1\"\n", "\n", "client = OpenAI(\n", "    base_url =  openai_api_base,\n", "    api_key = os.environ[\"OPENAI_API_KEY\"]\n", "    )\n", "\n", "\n", "def getAffiliations(paper_title, paper_text):\n", "    # Load organizations from file\n", "    with open('orgs.txt', 'r') as f:\n", "        valid_organizations = f.read().strip()\n", "\n", "    prompt = f\"\"\"\n", "You are tasked with extracting ALL author affiliations from a research paper and determining which ones match the provided notable organizations.\n", "\n", "Paper title: {paper_title}\n", "\n", "First page text: {paper_text}\n", "\n", "Notable organizations to check against:\n", "{valid_organizations}\n", "\n", "Extract ALL author affiliations from the paper, then:\n", "1. Mark each affiliation as notable (is_notable: true) if it matches any organization in the provided list\n", "2. Include all affiliations in all_affiliations\n", "3. Include only notable affiliations in notable_affiliations  \n", "4. Set has_notable_affiliations to true if any affiliations match the notable organizations list\n", "\n", "Return comprehensive affiliation data for all authors.\n", "\"\"\"\n", "\n", "    response = client.responses.parse(\n", "        model=\"gpt-5-nano\",\n", "        input=[\n", "            {\"role\": \"system\", \"content\": \"Extract ALL author affiliations from research papers and identify which match notable organizations.\"},\n", "            {\n", "                \"role\": \"user\", \n", "                \"content\": prompt\n", "                }\n", "        ],\n", "        text_format=PaperAffilationAnalysis,\n", "        reasoning={\n", "            \"effort\": \"minimal\",\n", "            \"summary\": None,\n", "        }\n", "    )\n", "\n", "    return {\n", "        \"input_tokens\": response.usage.input_tokens,\n", "        \"output_tokens\": response.usage.output_tokens,\n", "        \"affiliations\": response.output_parsed\n", "    }\n", "\n", "\n", "paper_index = 1\n", "paper_title = metadata['papers'][paper_index]['title']\n", "paper_text = getFirstPageText(metadata['papers'][paper_index])[\"text\"]\n", "\n", "response = getAffiliations(paper_title, paper_text)\n", "response[\"affiliations\"]"]}, {"cell_type": "code", "execution_count": 128, "id": "195d2e76", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 0 of 10: OmniParser for Pure Vision Based GUI Agent\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 10%|█         | 1/10 [00:03<00:27,  3.06s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 1 of 10: A Prior Embedding-Driven Architecture for Long Distance Blind Iris\n", "  Recognition\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 20%|██        | 2/10 [00:06<00:26,  3.33s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 2 of 10: Lost in Translation: Latent Concept Misalignment in Text-to-Image\n", "  Diffusion Models\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 30%|███       | 3/10 [00:10<00:24,  3.57s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 3 of 10: Multiple Greedy Quasi-Newton Methods for Saddle Point Problems\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 40%|████      | 4/10 [00:12<00:18,  3.04s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 4 of 10: RoCo:Robust Collaborative Perception By Iterative Object Matching and\n", "  Pose Adjustment\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 50%|█████     | 5/10 [00:16<00:16,  3.29s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 5 of 10: <PERSON>lover-2: Accurate Inference for Regressive Lightweight Speculative\n", "  Decoding\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 60%|██████    | 6/10 [00:19<00:12,  3.11s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 6 of 10: QUITO: Accelerating Long-Context Reasoning through Query-Guided Context\n", "  Compression\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 70%|███████   | 7/10 [00:22<00:10,  3.35s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 7 of 10: High Performance Im2win and Direct Convolutions using Three Tensor\n", "  Layouts on SIMD Architectures\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 80%|████████  | 8/10 [00:26<00:06,  3.37s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 8 of 10: Towards Scalable GPU-Accelerated SNN Training via Temporal Fusion\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 90%|█████████ | 9/10 [00:30<00:03,  3.49s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing paper 9 of 10: Gradient Harmonization in Unsupervised Domain Adaptation\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 10/10 [00:32<00:00,  3.26s/it]\n"]}], "source": ["from tqdm import tqdm\n", "import time\n", "\n", "\n", "affiliations = []\n", "\n", "papers = metadata['papers'][:10]\n", "\n", "for paper_index in tqdm(range(len(papers))):\n", "    \n", "    print(f\"Processing paper {paper_index} of {len(papers)}: {papers[paper_index]['title']}\")\n", "\n", "    total_start = time.time()\n", "    paper_title = metadata['papers'][paper_index]['title']\n", "    get_first_page_text_response = getFirstPageText(metadata['papers'][paper_index])\n", "    paper_text = get_first_page_text_response[\"text\"]\n", "    paper_source = get_first_page_text_response[\"source\"]\n", "    first_page_end = time.time()\n", "\n", "    response = getAffiliations(paper_title, paper_text)\n", "\n", "    total_end = time.time()\n", "\n", "    record = {\n", "        **metadata['papers'][paper_index],\n", "        \"affiliations\": response[\"affiliations\"],\n", "        \"input_tokens\": response[\"input_tokens\"],\n", "        \"output_tokens\": response[\"output_tokens\"],\n", "        \"total_processing_time\": total_end - total_start,\n", "        \"first_page_processing_time\": first_page_end - total_start,\n", "        \"affiliation_processing_time\": total_end - first_page_end,\n", "        \"paper_source\": paper_source\n", "    }\n", "\n", "    affiliations.append(record)\n", "    \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 146, "id": "f656565b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of affiliations:  10\n", "Average input tokens:  1092.2\n", "Average output tokens:  351.4\n", "Average total processing time:  3.26\n", "Average first page processing time:  0.26\n", "Average affiliation processing time:  3.0\n", "Source counts: \n", "\thtml \t9\n", "\tpdf \t1\n", "Average tokens per source:\n", "\thtml\t1098.11\t363.89\n", "\tpdf\t1039.0\t239.0\n", "Total cost:  0.001952\n"]}], "source": ["\n", "\n", "def getAverageInputTokens(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"input_tokens\"]\n", "    # to 2 decimal places\n", "    return round(total / len(affiliations), 2)\n", "\n", "def getAverageOutputTokens(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"output_tokens\"]\n", "    \n", "    # to 2 decimal places\n", "    return round(total / len(affiliations), 2)\n", "\n", "def getAverageTotalProcessingTime(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"total_processing_time\"]\n", "\n", "    # to 2 decimal places\n", "    return round(total / len(affiliations), 2)\n", "\n", "\n", "def getAverageFirstPageProcessingTime(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"first_page_processing_time\"]\n", "    # to 4 decimal places\n", "    return round(total / len(affiliations), 2)\n", "\n", "\n", "def getAverageAffiliationProcessingTime(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"affiliation_processing_time\"]\n", "    return round(total / len(affiliations),2)\n", "\n", "def getSourceCounts(affiliations):\n", "    counts = {}\n", "    for affiliation in affiliations:\n", "        source = affiliation[\"paper_source\"]\n", "        if source in counts:\n", "            counts[source] += 1\n", "        else:\n", "            counts[source] = 1\n", "    return counts\n", "\n", "def getAverageTokensPerSource(affiliations):\n", "    # gets average input and output tokens per source\n", "    source_counts = getSourceCounts(affiliations)\n", "    source_input_tokens = {}\n", "    source_output_tokens = {}\n", "    \n", "    for affiliation in affiliations:\n", "        source = affiliation[\"paper_source\"]\n", "        source_input_tokens[source] = source_input_tokens.get(source, 0) + affiliation[\"input_tokens\"]\n", "        source_output_tokens[source] = source_output_tokens.get(source, 0) + affiliation[\"output_tokens\"]\n", "    \n", "    # Calculate averages\n", "    source_avg_input = {}\n", "    source_avg_output = {}\n", "    for source in source_counts:\n", "        source_avg_input[source] = round(source_input_tokens[source] / source_counts[source], 2)\n", "        source_avg_output[source] = round(source_output_tokens[source] / source_counts[source], 2)\n", "    \n", "    return source_avg_input, source_avg_output\n", "\n", "def getCost(affiliations, price_per_input_token=0.05/1000000, price_per_output_token=0.4/1000000):\n", "    total_cost = 0\n", "    for affiliation in affiliations:\n", "        total_cost += affiliation[\"input_tokens\"] * price_per_input_token + affiliation[\"output_tokens\"] * price_per_output_token\n", "    # to 4 decimal places\n", "    return round(total_cost, 6)\n", "\n", "def printStatistics(affiliations):\n", "    print(\"Number of affiliations: \",len(affiliations))\n", "    print(\"Average input tokens: \",getAverageInputTokens(affiliations))\n", "    print(\"Average output tokens: \",getAverageOutputTokens(affiliations))\n", "    print(\"Average total processing time: \",getAverageTotalProcessingTime(affiliations))\n", "    print(\"Average first page processing time: \",getAverageFirstPageProcessingTime(affiliations))\n", "    print(\"Average affiliation processing time: \",getAverageAffiliationProcessingTime(affiliations))\n", "    print(\"Source counts: \")\n", "    for source in getSourceCounts(affiliations):\n", "        print(f\"\\t{source} \\t{getSourceCounts(affiliations)[source]}\")\n", "\n", "    # print each source, average input tokens, output tokens on new line and tab\n", "    source_avg_input, source_avg_output = getAverageTokensPerSource(affiliations)\n", "    print(\"Average tokens per source:\")\n", "    for source in source_avg_input:\n", "        print(f\"\\t{source}\\t{source_avg_input[source]}\\t{source_avg_output[source]}\")\n", "\n", "    print(\"Total cost: \",getCost(affiliations))\n", "\n", "printStatistics(affiliations)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}