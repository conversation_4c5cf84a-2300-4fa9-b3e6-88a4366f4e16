#!/usr/bin/env python3
"""
CLI to work with arXiv papers: fetch metadata, cache first pages, and extract affiliations.

This module also serves as the entrypoint for the `paperscraper` console script.
"""
from __future__ import annotations

import argparse
import json
import logging
import os
import sys
import time
from typing import List, Optional

DEFAULT_DATA_DIR = "data"
METADATA_SUBDIR = "metadata"
PAGES_SUBDIR = "pages"
AFFILIATIONS_SUBDIR = "affiliations"


def configure_logging(verbosity: int) -> None:
    level = logging.WARNING
    if verbosity == 1:
        level = logging.INFO
    elif verbosity >= 2:
        level = logging.DEBUG
    logging.basicConfig(level=level, format="%(levelname)s: %(message)s")



def _date_parts(date: str):
    y, m, d = date.split("-")
    return y[2:], m, d


def _type_day_dir(root: str, type_subdir: str, date: str) -> str:
    yy, mm, dd = _date_parts(date)
    return os.path.join(root, type_subdir, yy, mm, dd)


def cmd_metadata(args: argparse.Namespace) -> int:
    from paperscraper.metadata import fetch_cs_ai_metadata_for_date  # lazy import

    date = args.date
    root = args.data_dir

    # Ensure data/metadata/YY/MM/DD
    out_dir = _type_day_dir(root, METADATA_SUBDIR, date)
    os.makedirs(out_dir, exist_ok=True)

    # If metadata already exists and not forcing, skip
    out_path = os.path.join(out_dir, "metadata.json")
    if os.path.exists(out_path) and not args.force:
        logging.info("Metadata already exists: %s (use --force to refresh)", out_path)
        return 0

    try:
        items = fetch_cs_ai_metadata_for_date(date, limit=args.num)
    except ValueError as e:
        logging.error(str(e))
        print(f"Error: {e}", file=sys.stderr)
        return 2
    except Exception as e:
        logging.error("Failed to fetch from arXiv: %s", e)
        print(f"Network/API error: {e}", file=sys.stderr)
        return 4

    if not items:
        print("No papers found for that date.")
        return 3

    try:
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump({"date": date, "category": "cs.AI", "count": len(items), "papers": items}, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.error("Failed to write %s: %s", out_path, e)
        print(f"Filesystem error writing {out_path}: {e}", file=sys.stderr)
        return 5

    # Print URLs for convenience
    for u in (item.get("abs_url") for item in items if item.get("abs_url")):
        print(u)
    return 0



def _page_cache_path(pages_root: str, date: str, paper: dict) -> str:
    # pages_root is already data/pages/YY/MM/DD; just join id
    arxiv_id = (paper.get("abs_url") or "").rstrip("/").split("/")[-1]
    os.makedirs(pages_root, exist_ok=True)
    return os.path.join(pages_root, f"{arxiv_id}.txt")


def _old_page_cache_path(pages_root: str, paper: dict) -> str:
    arxiv_id = (paper.get("abs_url") or "").rstrip("/").split("/")[-1]
    return os.path.join(pages_root, f"{arxiv_id}.txt")



def cmd_pages(args: argparse.Namespace) -> int:
    # Read metadata then save first-page text for num papers to data/pages/YY/MM/DD/<id>.txt
    from paperscraper.page import getFirstPageText

    try:
        metadata = _load_metadata(args.input_file, args.param or args.date, args.data_dir)
    except Exception as e:
        print(f"Failed to read metadata: {e}", file=sys.stderr)
        return 5


    # data/pages/YY/MM/DD
    date = metadata.get("date") or args.date or args.param
    if not date:
        print("Date is required for pages command (in metadata or via --date)", file=sys.stderr)
        return 2
    pages_root = _type_day_dir(args.data_dir, PAGES_SUBDIR, date)
    os.makedirs(pages_root, exist_ok=True)

    # Migrate any legacy flat files for papers in this metadata (old location: data/pages/<id>.txt)
    legacy_migrated = 0
    for p in (metadata.get("papers") or []):
        old_path = _old_page_cache_path(os.path.join(args.data_dir, PAGES_SUBDIR), p)
        new_path = _page_cache_path(args.data_dir, date, p)
        if os.path.exists(old_path) and not os.path.exists(new_path):
            try:
                os.makedirs(os.path.dirname(new_path), exist_ok=True)
                os.replace(old_path, new_path)
                legacy_migrated += 1
            except Exception as e:
                logging.warning("Failed to migrate legacy page %s -> %s: %s", old_path, new_path, e)
    if legacy_migrated:
        logging.info("Migrated %d legacy page files into new structure", legacy_migrated)

    pages_root = os.path.join(args.data_dir, PAGES_SUBDIR)
    papers = (metadata.get("papers") or [])[: (args.num or 5)]
    if not papers:
        print("No papers to process.")
        return 3

    for paper in papers:
        cache_path = _page_cache_path(pages_root, date, paper)
        if os.path.exists(cache_path) and not args.force:
            logging.info("Page exists, skipping: %s", cache_path)
            continue
        info = getFirstPageText(paper, cache_path=cache_path)
        if not info.get("text"):
            logging.warning("Empty page text for %s", cache_path)

    return 0


def _load_metadata(input_file: Optional[str], date: Optional[str], data_dir: str) -> dict:
    if input_file:
        with open(input_file, "r", encoding="utf-8") as f:
            return json.load(f)
    if date:
        path = os.path.join(data_dir, METADATA_SUBDIR, date, "metadata.json")
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    raise ValueError("Either --input-file or --date must be provided")


def cmd_affiliations(args: argparse.Namespace) -> int:
    # Lazy imports to avoid requiring optional deps (bs4, openai, PyPDF2) for other commands
    from paperscraper.page import getFirstPageText
    from paperscraper.llm import getAffiliations
    from paperscraper.metrics import printStatistics

    try:
        metadata = _load_metadata(args.input_file, args.date, args.data_dir)
    except Exception as e:
        print(f"Failed to read metadata: {e}", file=sys.stderr)
        return 5

    papers = metadata.get("papers", [])
    if not papers:
        print("No papers available to process.")
        return 3

    # data/affiliations/YY/MM/DD
    date = metadata.get("date") or args.date
    aff_dir = _type_day_dir(args.data_dir, AFFILIATIONS_SUBDIR, date or "1970-01-01")
    os.makedirs(aff_dir, exist_ok=True)

    results = []
    for i, paper in enumerate(papers, start=1):
        logging.info("Processing paper %d/%d: %s", i, len(papers), paper.get("title", "(untitled)"))
        total_start = time.time()

        pages_root = _type_day_dir(args.data_dir, PAGES_SUBDIR, date) if date else None
        first_start = time.time()
        if pages_root and date:
            cache_path = _page_cache_path(pages_root, date, paper)
        else:
            cache_path = None
        if cache_path and os.path.exists(cache_path) and not args.force:
            # Reuse existing cached page
            try:
                with open(cache_path, "r", encoding="utf-8") as f:
                    text = f.read()
                page_info = {"source": "cache", "text": text}
            except Exception:
                page_info = getFirstPageText(paper, cache_path=cache_path)
        else:
            page_info = getFirstPageText(paper, cache_path=cache_path)
        first_end = time.time()

        aff_start = time.time()
        aff_resp = getAffiliations(
            paper_title=paper.get("title", ""),
            paper_text=str(page_info.get("text") or ""),
            provider=args.provider,
            api_key=args.api_key,
            base_url=args.base_url,
            model=args.model,
            orgs_path=args.orgs,
        )
        aff_end = time.time()

        record = {
            **paper,
            "affiliations": aff_resp.get("affiliations"),
            "input_tokens": aff_resp.get("input_tokens", 0),
            "output_tokens": aff_resp.get("output_tokens", 0),
            "paper_source": page_info.get("source", "none"),
            "first_page_processing_time": round(first_end - first_start, 2),
            "affiliation_processing_time": round(aff_end - aff_start, 2),
            "total_processing_time": round(aff_end - total_start, 2),
        }
        results.append(record)

    # Persist results as JSON alongside date or in a flat file
    out_name = args.output or "affiliations.json"
    out_path = os.path.join(aff_dir, out_name)
    try:
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump({"count": len(results), "items": results}, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.error("Failed to write %s: %s", out_path, e)
        print(f"Filesystem error writing {out_path}: {e}", file=sys.stderr)
        return 5

    printStatistics(results)
    return 0


def build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description="Work with arXiv papers: metadata, pages, affiliations.")
    p.add_argument("command", choices=["metadata", "pages", "affiliations"], help="Which action to perform")
    p.add_argument("param", nargs="?", help="For 'metadata'/'pages': date (YYYY-MM-DD)")
    p.add_argument("--data-dir", default=DEFAULT_DATA_DIR, help="Root data directory (default: data)")
    p.add_argument("--verbose", "-v", action="count", default=0, help="Increase logging verbosity (-v, -vv)")

    # metadata options
    p.add_argument("--num", type=int, default=5, help="Max number of papers to fetch for metadata (default: 5)")
    p.add_argument("--force", action="store_true", help="Force refresh existing outputs")

    # shared options
    p.add_argument("--date", help="Date (YYYY-MM-DD) to read metadata from data/metadata/<date>/metadata.json")
    p.add_argument("--input-file", help="Path to JSON file containing papers list with arxiv IDs")

    # affiliations options
    p.add_argument("--output", help="Output filename inside data/affiliations")
    p.add_argument("--provider", choices=["openai", "ollama"], default="openai", help="LLM provider")
    p.add_argument("--api-key", help="API key (ignored for ollama; default env vars used)")
    p.add_argument("--base-url", help="Override base URL (optional)")
    p.add_argument("--model", help="Model name (default via AFFILIATIONS_MODEL env var)")
    p.add_argument("--orgs", default="orgs.txt", help="Path to organizations list (default: orgs.txt)")

    return p


def main(argv: List[str]) -> int:
    parser = build_parser()
    args = parser.parse_args(argv)
    configure_logging(args.verbose)

    if args.command == "metadata":
        if not args.param:
            print("Usage: papers.py metadata YYYY-MM-DD", file=sys.stderr)
            return 2
        args.date = args.param
        return cmd_metadata(args)

    if args.command == "pages":
        if not args.param and not args.date and not args.input_file:
            print("Usage: papers.py pages YYYY-MM-DD or --date or --input-file", file=sys.stderr)
            return 2
        return cmd_pages(args)

    if args.command == "affiliations":
        if not args.date and not args.input_file:
            print("Provide --date or --input-file for affiliations command", file=sys.stderr)
            return 2
        return cmd_affiliations(args)

    print("Unknown command", file=sys.stderr)
    return 2


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))
