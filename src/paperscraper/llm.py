"""
LLM processing for extracting affiliations.

Provides getAffiliations that uses the OpenAI client with selectable provider:
- provider="openai": base_url https://api.openai.com/v1 (requires OPENAI_API_KEY)
- provider="ollama": base_url http://localhost:11434/v1 (api_key can be any string, e.g., "ollama")

Returns a dict with input_tokens, output_tokens, and affiliations (pydantic model serialized to dict).
"""
from __future__ import annotations

from typing import Dict, Optional, List
from pydantic import BaseModel
from openai import OpenAI
import os
import json
import re


class AuthorAffiliation(BaseModel):
    author_name: str
    organization: str


class AllAuthorAffiliation(BaseModel):
    author_name: str
    organization: str
    is_notable: bool


class PaperAffilationAnalysis(BaseModel):
    paper_title: str
    all_affiliations: List[AllAuthorAffiliation]
    notable_affiliations: List[AuthorAffiliation]
    has_notable_affiliations: bool


OLLAMA_BASE = "http://localhost:11434/v1"
OPENAI_BASE = "https://api.openai.com/v1"
DEFAULT_MODEL = os.getenv("AFFILIATIONS_MODEL", "gpt-5-nano")


def _get_client(provider: str = "openai", api_key: Optional[str] = None, base_url: Optional[str] = None) -> OpenAI:
    provider = (provider or "openai").lower()
    if provider == "ollama":
        return OpenAI(base_url=base_url or OLLAMA_BASE, api_key=api_key or os.getenv("OLLAMA_API_KEY", "ollama"))
    # default openai
    key = api_key or os.getenv("OPENAI_API_KEY")
    if not key:
        raise RuntimeError("OPENAI_API_KEY is required for provider 'openai'.")
    return OpenAI(base_url=base_url or OPENAI_BASE, api_key=key)


def _strip_code_fences(s: str) -> str:
    # remove ```json ... ``` fences if present
    return re.sub(r"^```[a-zA-Z]*\n|\n```$", "", s.strip())


def _ollama_affiliations(client: OpenAI, model: str, prompt: str) -> Dict:
    # Use chat.completions and request strict JSON output to match schema
    sys_msg = "Extract ALL author affiliations and return ONLY valid JSON matching the required schema."
    json_instr = {
        "paper_title": "string",
        "all_affiliations": [
            {"author_name": "string", "organization": "string", "is_notable": True}
        ],
        "notable_affiliations": [
            {"author_name": "string", "organization": "string"}
        ],
        "has_notable_affiliations": True
    }

    completion = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "system", "content": sys_msg},
            {"role": "user", "content": f"Return ONLY JSON with this schema (no extra text):\n{json.dumps(json_instr)}\n\nTask:\n{prompt}"},
        ],
        temperature=0,
    )
    content = completion.choices[0].message.content or "{}"
    content = _strip_code_fences(content)
    try:
        obj = json.loads(content)
    except Exception:
        obj = {}

    # Validate using pydantic model; fallback to best-effort
    try:
        parsed = PaperAffilationAnalysis(**obj)
        parsed_dict = parsed.model_dump()
    except Exception:
        parsed_dict = obj

    usage = getattr(completion, "usage", None)
    input_toks = getattr(usage, "prompt_tokens", 0) if usage else 0
    output_toks = getattr(usage, "completion_tokens", 0) if usage else 0

    return {
        "input_tokens": input_toks,
        "output_tokens": output_toks,
        "affiliations": parsed_dict,
    }


def getAffiliations(paper_title: str, paper_text: str, *, provider: str = "openai", api_key: Optional[str] = None, base_url: Optional[str] = None, model: Optional[str] = None, orgs_path: str = "orgs.txt") -> Dict:
    # Load organizations from file (one per line or comma-separated; use as-is text block)
    with open(orgs_path, "r", encoding="utf-8") as f:
        valid_organizations = f.read().strip()

    prompt = f"""
You are tasked with extracting ALL author affiliations from a research paper and determining which ones match the provided notable organizations.

Paper title: {paper_title}

First page text: {paper_text}

Notable organizations to check against:
{valid_organizations}

Extract ALL author affiliations from the paper, then:
1. Mark each affiliation as notable (is_notable: true) if it matches any organization in the provided list
2. Include all affiliations in all_affiliations
3. Include only notable affiliations in notable_affiliations
4. Set has_notable_affiliations to true if any affiliations match the notable organizations list

Return comprehensive affiliation data for all authors.
"""

    client = _get_client(provider=provider, api_key=api_key, base_url=base_url)

    chosen_model = model or DEFAULT_MODEL

    if (provider or "openai").lower() == "ollama":
        return _ollama_affiliations(client, chosen_model, prompt)

    # OpenAI default: use Responses.parse for structured output
    response = client.responses.parse(
        model=chosen_model,
        input=[
            {"role": "system", "content": "Extract ALL author affiliations from research papers and identify which match notable organizations."},
            {"role": "user", "content": prompt},
        ],
        text_format=PaperAffilationAnalysis,
        reasoning={
            "effort": "minimal",
            "summary": None,
        },
    )

    parsed = response.output_parsed
    # pydantic v2 model_dump, fallback to dict()
    try:
        parsed_dict = parsed.model_dump()
    except Exception:
        try:
            parsed_dict = parsed.dict()
        except Exception:
            parsed_dict = parsed

    return {
        "input_tokens": getattr(response.usage, "input_tokens", 0),
        "output_tokens": getattr(response.usage, "output_tokens", 0),
        "affiliations": parsed_dict,
    }
